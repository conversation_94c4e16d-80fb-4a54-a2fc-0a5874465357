import { lazy } from 'react'
import { ADMIN } from '@/constants/roles.constant'
import type { Routes } from '@/@types/routes'

const dashboardRoute: Routes = [
    {
        key: 'dashboard',
        path: `dashboard`,
        component: lazy(() => import('@/views/Dashboard')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'contained',
        },
    },
    
]

export default dashboardRoute
