import { Routes } from '@/@types/routes';
import { ADMIN } from '@/constants/roles.constant';
import { LISTS_PREFIX_PATH } from '@/constants/route.constant';
import { lazy } from 'react'



const listsRoute : Routes = [
    {
        key: 'lists.administrations',
        path: `${LISTS_PREFIX_PATH}/administrations`,
        component: lazy(() => import('@/views/lists/Administrations')),
        authority: [ADMIN],
                meta: {
                    pageContainerType: 'contained',
                },
    }
];

export default listsRoute;